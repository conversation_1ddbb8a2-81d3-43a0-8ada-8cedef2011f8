name = "deploy-ql-3-node-worker"
compatibility_date = "2024-12-01"
main = "src/index.js"

compatibility_flags = ["nodejs_compat"]

[vars]
SUPABASE_URL = "https://kwtpkihipcokqbrfvxhn.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt3dHBraWhpcGNva3FicmZ2eGhuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MTAyNzcwNywiZXhwIjoyMDU2NjAzNzA3fQ.9a0x65zz_CMGGZRe-akZdDyelFOTZXGjVe7lpsDXW70"
OPENAI_API_KEY = "AIzaSyBA1s5IGCtTEnlzvjKgHDWfBnr0llzJjd0"
OPENAI_API_KEY1 = "AIzaSyCrmZLS_IiiFpB_VJgUxqN-Swv8dASGclk"
GEMINI_BASE_URL = "https://generativelanguage.googleapis.com/v1beta/openai/"
GEMINI_MODEL = "gemini-2.0-flash"
MAX_REQUESTS_PER_DAY = "1500"
MAX_REQUESTS_PER_MINUTE = "15"