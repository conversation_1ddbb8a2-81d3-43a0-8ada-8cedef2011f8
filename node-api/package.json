{"name": "deploy-ql-3-node-api", "version": "1.0.0", "description": "Node.js implementation of the Q Legal API", "main": "src/index.js", "scripts": {"deploy": "wrangler deploy", "start": "wrangler dev", "test": "jest"}, "dependencies": {"@supabase/supabase-js": "^2.49.8", "express": "^4.19.2", "express-rate-limit": "^7.5.0", "openai": "^4.52.0"}, "devDependencies": {"wrangler": "^3.0.0"}, "engines": {"node": ">=18.0.0"}, "module": "src/index.js"}