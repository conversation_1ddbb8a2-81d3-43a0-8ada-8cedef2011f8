const { createClient } = require('@supabase/supabase-js');

/**
 * Verifies user credentials and credit balance
 * @param {string} userId - User ID from request
 * @param {string} apiKey - API key from request
 * @returns {Promise<string>} - Verified user ID
 * @throws {Error} - For authentication failures
 */
async function verifyUser(userId, apiKey) {
  // Validate input parameters
  if (!userId || !apiKey) {
    throw new Error('Missing user_id or api_key in request');
  }

  // Initialize Supabase client
  const supabase = createClient(
    process.env.SUPABASE_URL, 
    process.env.SUPABASE_KEY
  );

  try {
    // Query user profile
    const { data, error } = await supabase
      .from('profiles')
      .select('api_key, credits')
      .eq('user_id', userId);

    if (error) {
      console.error(`Supabase query error: ${error.message}`);
      throw new Error('Failed to fetch user profile');
    }

    if (!data || data.length === 0) {
      throw new Error(`User profile not found for user_id: ${userId}`);
    }

    const profile = data[0];

    // Verify API key
    if (profile.api_key !== apiKey) {
      throw new Error('Invalid API key');
    }

    // Check credit balance
    if (profile.credits <= 0) {
      throw new Error('Insufficient credits');
    }

    return userId;
  } catch (error) {
    console.error(`Authentication failed: ${error.message}`);
    throw error;
  }
}

module.exports = { verifyUser };