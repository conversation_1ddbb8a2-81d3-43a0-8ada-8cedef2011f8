import { verifyUser } from './auth.js';
import { queryG<PERSON>ini } from './ai.js';
import { consumeCredit } from './credits.js';

// Helper function to add CORS headers
function addCorsHeaders(response) {
  response.headers.set('Access-Control-Allow-Origin', '*');
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  return response;
}

export default {
  async fetch(request, env) {
    const url = new URL(request.url);

    // CORS handling for preflight requests
    if (request.method === 'OPTIONS') {
      return handleOptions(request);
    }

    // Health check
    if (url.pathname === '/health') {
      const response = new Response('OK', { status: 200 });
      return addCorsHeaders(response);
    }

    // Status endpoint
    if (url.pathname === '/status') {
      const response = new Response(JSON.stringify({ status: 'API operational' }), {
        headers: { 'Content-Type': 'application/json' },
        status: 200
      });
      return addCorsHeaders(response);
    }

    // Main legal endpoint
    if (url.pathname === '/legal' && request.method === 'POST') {
      try {
        // Rate limiting (simplified for Workers, can be enhanced with KV)
        const MAX_REQUESTS_PER_MINUTE = parseInt(env.MAX_REQUESTS_PER_MINUTE) || 15;
        // This is a very basic in-memory rate limit. For production, use Cloudflare KV.
        // For now, we'll skip a full rate limit implementation to get the worker running.
        // A proper rate limit would involve storing request counts in `env.KV_NAMESPACE`.

        const { user_id, api_key, question } = await request.json();
        
        // Verify user credentials
        await verifyUser(user_id, api_key, env);

        // Query AI model
        const aiResponse = await queryGemini(question, env);

        // Deduct credit
        await consumeCredit(user_id, env);

        const response = new Response(JSON.stringify({ response: aiResponse }), {
          headers: { 'Content-Type': 'application/json' },
          status: 200
        });
        return addCorsHeaders(response);
      } catch (error) {
        console.error(`Legal endpoint error: ${error.message}`);
        const errorResponse = new Response(JSON.stringify({ error: error.message }), {
          headers: { 'Content-Type': 'application/json' },
          status: 500
        });
        return addCorsHeaders(errorResponse);
      }
    }

    // Fallback for unknown routes
    const notFoundResponse = new Response('Not Found', { status: 404 });
    return addCorsHeaders(notFoundResponse);
  }
};

function handleOptions(request) {
  const headers = request.headers;
  if (
    headers.get('Origin') !== null &&
    headers.get('Access-Control-Request-Method') !== null &&
    headers.get('Access-Control-Request-Headers') !== null
  ) {
    // Handle CORS preflight requests
    return new Response(null, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET,HEAD,POST,OPTIONS',
        'Access-Control-Max-Age': '86400',
        'Access-Control-Allow-Headers': headers.get('Access-Control-Request-Headers'),
      },
    });
  } else {
    // Handle standard OPTIONS request.
    return new Response(null, {
      headers: {
        'Allow': 'GET, HEAD, POST, OPTIONS',
      },
    });
  }
}