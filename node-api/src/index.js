import { verifyUser } from './auth.js';
import { queryGemini } from './ai.js';
import { consumeCredit } from './credits.js';

export default {
  async fetch(request, env) {
    const url = new URL(request.url);

    // CORS handling for preflight requests
    if (request.method === 'OPTIONS') {
      return handleOptions(request);
    }

    // Health check
    if (url.pathname === '/health') {
      return new Response('OK', { status: 200 });
    }

    // Status endpoint
    if (url.pathname === '/status') {
      return new Response(JSON.stringify({ status: 'API operational' }), {
        headers: { 'Content-Type': 'application/json' },
        status: 200
      });
    }

    // Main legal endpoint
    if (url.pathname === '/legal' && request.method === 'POST') {
      try {
        // Rate limiting (simplified for Workers, can be enhanced with KV)
        const MAX_REQUESTS_PER_MINUTE = parseInt(env.MAX_REQUESTS_PER_MINUTE) || 15;
        // This is a very basic in-memory rate limit. For production, use Cloudflare KV.
        // For now, we'll skip a full rate limit implementation to get the worker running.
        // A proper rate limit would involve storing request counts in `env.KV_NAMESPACE`.

        const { user_id, api_key, question } = await request.json();
        
        // Verify user credentials
        await verifyUser(user_id, api_key);
        
        // Query AI model
        const response = await queryGemini(question);
        
        // Deduct credit
        await consumeCredit(user_id);
        
        return new Response(JSON.stringify({ response }), {
          headers: { 'Content-Type': 'application/json' },
          status: 200
        });
      } catch (error) {
        console.error(`Legal endpoint error: ${error.message}`);
        return new Response(JSON.stringify({ error: error.message }), {
          headers: { 'Content-Type': 'application/json' },
          status: 500
        });
      }
    }

    // Fallback for unknown routes
    return new Response('Not Found', { status: 404 });
  }
};

function handleOptions(request) {
  const headers = request.headers;
  if (
    headers.get('Origin') !== null &&
    headers.get('Access-Control-Request-Method') !== null &&
    headers.get('Access-Control-Request-Headers') !== null
  ) {
    // Handle CORS preflight requests
    return new Response(null, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET,HEAD,POST,OPTIONS',
        'Access-Control-Max-Age': '86400',
        'Access-Control-Allow-Headers': headers.get('Access-Control-Request-Headers'),
      },
    });
  } else {
    // Handle standard OPTIONS request.
    return new Response(null, {
      headers: {
        'Allow': 'GET, HEAD, POST, OPTIONS',
      },
    });
  }
}