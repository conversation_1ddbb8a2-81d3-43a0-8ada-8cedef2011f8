const { createClient } = require('@supabase/supabase-js');

/**
 * Consumes one credit for the specified user
 * @param {string} userId - User ID to deduct credit from
 * @returns {Promise<void>}
 * @throws {Error} If credit deduction fails
 */
async function consumeCredit(userId) {
  if (!userId) {
    throw new Error('User ID is required for credit consumption');
  }

  const supabase = createClient(
    process.env.SUPABASE_URL, 
    process.env.SUPABASE_KEY
  );

  try {
    const { error } = await supabase.rpc('decrement_credits', {
      user_uuid: userId
    });

    if (error) {
      console.error(`Credit consumption failed for ${userId}: ${error.message}`);
      throw new Error('Failed to update user credits');
    }

    console.log(`Successfully consumed credit for user: ${userId}`);
  } catch (error) {
    console.error(`Credit consumption error: ${error.message}`);
    throw new Error('Credit system unavailable');
  }
}

module.exports = { consumeCredit };