import { createClient } from '@supabase/supabase-js';

/**
 * Consumes one credit for the specified user
 * @param {string} userId - User ID to deduct credit from
 * @param {Object} env - Cloudflare Workers environment variables
 * @returns {Promise<void>}
 * @throws {Error} If credit deduction fails
 */
export async function consumeCredit(userId, env) {
  if (!userId) {
    throw new Error('User ID is required for credit consumption');
  }

  const supabase = createClient(
    env.SUPABASE_URL,
    env.SUPABASE_KEY
  );

  try {
    const { error } = await supabase.rpc('decrement_credits', {
      user_uuid: userId
    });

    if (error) {
      console.error(`Credit consumption failed for ${userId}: ${error.message}`);
      throw new Error('Failed to update user credits');
    }

    console.log(`Successfully consumed credit for user: ${userId}`);
  } catch (error) {
    console.error(`Credit consumption error: ${error.message}`);
    throw new Error('Credit system unavailable');
  }
}

