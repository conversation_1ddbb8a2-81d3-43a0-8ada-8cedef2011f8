import { OpenAI } from 'openai';

// Initialize OpenAI clients with key rotation
let clients = [];
let currentIndex = 0;

// Initialize clients from environment variables
function initializeClients(env) {
  const keys = [
    env.OPENAI_API_KEY,
    env.OPENAI_API_KEY1
  ].filter(key => key);

  if (keys.length === 0) {
    throw new Error('No valid OpenAI API keys found in environment variables');
  }

  clients = keys.map(key => new OpenAI({
    apiKey: key,
    baseURL: env.GEMINI_BASE_URL
  }));

  console.log(`Initialized ${clients.length} OpenAI clients for Gemini`);
}

// Query Gemini with automatic key rotation and retry
export async function queryGemini(prompt, env) {
  if (clients.length === 0) {
    initializeClients(env);
  }

  const numClients = clients.length;

  for (let attempt = 0; attempt < numClients; attempt++) {
    const client = clients[currentIndex];
    currentIndex = (currentIndex + 1) % numClients;

    try {
      const response = await client.chat.completions.create({
        model: env.GEMINI_MODEL || 'gemini-2.0-flash',
        messages: [
          {
            role: "system", 
            content: "Tu es un expert en droit français, spécialisé dans les questions juridiques et légales. Tu apportes des réponses claires, précises et fiables, en t'appuyant sur la législation française en vigueur, la jurisprudence pertinente et les principes fondamentaux du droit."
          },
          {
            role: "user", 
            content: prompt
          }
        ],
        max_tokens: 2048,
        temperature: 0.7
      });

      if (!response.choices?.[0]?.message?.content) {
        throw new Error('AI model returned an empty response');
      }

      return response.choices[0].message.content;
    } catch (error) {
      console.error(`Error with client ${currentIndex}: ${error.message}`);
      
      // Handle rate limits specifically
      if (error.status === 429 && attempt < numClients - 1) {
        console.log(`Rate limit exceeded, trying next client...`);
        continue;
      }
      
      // For other errors, rethrow after all attempts
      if (attempt === numClients - 1) {
        throw new Error(`All API keys failed: ${error.message}`);
      }
    }
  }
}

