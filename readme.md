# Q Legale API (Node.js Implementation)

## Overview
This project has been converted from Python to Node.js to improve performance and maintainability. The API provides legal question answering using Gemini AI models with Supabase authentication and credit management.

## Project Structure
```
node-api/
├── src/
│   ├── index.js         # Main entry point
│   ├── auth.js          # Authentication handlers
│   ├── ai.js            # AI query handling
│   ├── credits.js       # Credit management
│   └── utils.js         # Utility functions
├── package.json
├── wrangler.toml
└── README.md
```

## Setup Instructions

### 1. Install Dependencies
```bash
cd node-api
npm install
```

### 2. Configure Environment Variables
Create `.env` file in the project root:
```ini
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
OPENAI_API_KEY=your_openai_key
GEMINI_BASE_URL=https://generativelanguage.googleapis.com/v1beta/openai/
GEMINI_MODEL=gemini-2.0-flash
MAX_REQUESTS_PER_MINUTE=15
```

### 3. Local Development
```bash
npm run dev
```
API will be available at http://localhost:3000

### 4. Cloudflare Deployment
```bash
# Install Wrangler globally
npm install -g wrangler

# Authenticate with Cloudflare
npx wrangler login

# Deploy the worker
npx wrangler deploy
```

## API Endpoints

### POST /legal
Submit legal questions to the AI model
```json
{
  "user_id": "user-uuid",
  "api_key": "user-api-key",
  "question": "Legal question here"
}
```

### GET /status
Check API status
```bash
curl http://localhost:3000/status
```

### GET /health
Health check endpoint
```bash
curl http://localhost:3000/health
```

